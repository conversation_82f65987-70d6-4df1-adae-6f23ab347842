<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialTemplateExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;

    /**
    * @return Collection
    */
    public function collection()
    {
        // Return sample data for template
        return collect([
            [
                'title' => 'Ôn tương đầu tiên của em',
                'content_url' => '/he-1-01',
                'content' => 'a',
                'document_url' => 'LTH',
                'course' => 'h1.01'
            ],
            [
                'title' => 'Em tìm hiểu nơi quy học tiểu học',
                'content_url' => '/he-1-02',
                'content' => 'a',
                'document_url' => 'LTH',
                'course' => 'h1.02'
            ]
        ]);
    }

    public function headings(): array
    {
        return [
            'Tiêu đề',
            'URL',
            'Nội dung',
            'Giáo án',
            'Khóa học'
        ];
    }

    public function map($row): array
    {
        return [
            $row['title'],
            $row['content_url'],
            $row['content'],
            $row['document_url'],
            $row['course']
        ];
    }
}
