<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialTemplateExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;

    /**
    * @return Collection
    */
    public function collection()
    {
        // Return sample data for template
        return collect([
            [
                'course_key' => 'sample-course-1',
                'lesson_title' => 'Lesson 1: Introduction',
                'title' => 'Sample Material 1',
                'type' => 'video',
                'content' => 'Introduction to the course',
                'content_url' => 'https://example.com/video1.mp4',
                'document_url' => 'https://example.com/doc1.pdf'
            ],
            [
                'course_key' => 'sample-course-2',
                'lesson_title' => 'Lesson 2: Advanced Topics',
                'title' => 'Sample Material 2',
                'type' => 'document',
                'content' => 'Advanced concepts and examples',
                'content_url' => 'https://example.com/content2.html',
                'document_url' => 'https://example.com/doc2.pdf'
            ]
        ]);
    }

    public function headings(): array
    {
        return [
            'Course Key',
            'Lesson Title',
            'Material Title',
            'Type',
            'Content',
            'Content URL',
            'Document URL'
        ];
    }

    public function map($row): array
    {
        return [
            $row['course_key'],
            $row['lesson_title'],
            $row['title'],
            $row['type'],
            $row['content'],
            $row['content_url'],
            $row['document_url']
        ];
    }
}
