<?php

namespace App\Exports;

use App\Models\Material;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    private $stt;

    public function __construct()
    {
        $this->stt = 0;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        try {
            \Log::info('MaterialExport collection() called');
            $materials = Material::query()
                ->with(['lesson.course'])
                ->get();
            \Log::info('Found ' . $materials->count() . ' materials');
            return $materials;
        } catch (\Exception $e) {
            \Log::error('Error in MaterialExport collection: ' . $e->getMessage());
            throw $e;
        }
    }

    public function headings(): array
    {
        return [
            'Tiêu đề',
            'URL',
            'Nội dung',
            '<PERSON><PERSON><PERSON><PERSON> án',
            '<PERSON>h<PERSON><PERSON> học'
        ];
    }

    public function map($material): array
    {
        $this->stt++;

        // Get course key, fallback to course ID if key is null
        $courseKey = '';
        if ($material->lesson && $material->lesson->course) {
            $courseKey = $material->lesson->course->key ?: $material->lesson->course->id;
        }

        return [
            $material->title ?? '',
            $material->content_url ?? '',
            $material->content ?? '',
            $material->document_url ?? '',
            $courseKey
        ];
    }
}
