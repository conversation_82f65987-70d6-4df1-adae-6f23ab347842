<?php

namespace App\Exports;

use App\Models\Material;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    private $stt;

    public function __construct()
    {
        $this->stt = 0;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return Material::query()
            ->with(['lesson.course'])
            ->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Tiêu đề',
            'URL',
            'Nội dung',
            'Giáo án',
            'Khóa học'
        ];
    }

    public function map($material): array
    {
        $this->stt++;

        // Get course key, fallback to course ID if key is null
        $courseKey = '';
        if ($material->lesson && $material->lesson->course) {
            $courseKey = $material->lesson->course->key ?: $material->lesson->course->id;
        }

        return [
            $material->id,
            $material->title ?? '',
            $material->content_url ?? '',
            $material->content ?? '',
            $material->document_url ?? '',
            $courseKey
        ];
    }
}
