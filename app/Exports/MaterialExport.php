<?php

namespace App\Exports;

use App\Models\Material;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    private $stt;

    public function __construct()
    {
        $this->stt = 0;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return Material::query()
            ->with(['lesson.course'])
            ->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Tiêu đề',
            'URL',
            'Nội dung',
            'Giáo án',
            'Khóa học'
        ];
    }

    public function map($material): array
    {
        $this->stt++;

        // Get course title
        $courseTitle = '';
        if ($material->lesson && $material->lesson->course) {
            $courseTitle = $material->lesson->course->title_en;
        }

        return [
            $material->id,
            $material->title ?? '',
            $material->content_url ?? '',
            $material->content ?? '',
            $material->document_url ?? '',
            $courseTitle
        ];
    }
}
