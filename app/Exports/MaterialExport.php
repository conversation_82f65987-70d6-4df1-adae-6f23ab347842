<?php

namespace App\Exports;

use App\Models\Material;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    private $stt;

    public function __construct()
    {
        $this->stt = 0;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return Material::query()
            ->with(['lesson.course'])
            ->select([
                'id',
                'lesson_id',
                'title',
                'type',
                'content',
                'content_url',
                'document_url',
                'created_at'
            ])
            ->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Course Key',
            'Lesson Title',
            'Material Title',
            'Type',
            'Content',
            'Content URL',
            'Document URL',
            'Created At'
        ];
    }

    public function map($material): array
    {
        $this->stt++;
        return [
            $material->id,
            $material->lesson && $material->lesson->course ? $material->lesson->course->key : '',
            $material->lesson ? $material->lesson->title : '',
            $material->title,
            $material->type,
            $material->content,
            $material->content_url,
            $material->document_url,
            $material->created_at ? $material->created_at->format('Y-m-d H:i:s') : ''
        ];
    }
}
