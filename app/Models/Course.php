<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Course extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['key', 'title_en', 'title_bn', 'description_en', 'description_bn', 'type', 'price', 'old_price',
        'subscription_price', 'start_from', 'duration', 'lesson', 'prerequisite_en', 'prerequisite_bn', 'difficulty',
        'course_code', 'image', 'thumbnail_image', 'thumbnail_video', 'tag', 'status', 'language', 'deleted_at', 'material_prefix'];

    public function lesson()
    {
        return $this->hasMany(Lesson::class);
    }

    public function enrollment()
    {
        return $this->hasMany(Enrollment::class);
    }
}
