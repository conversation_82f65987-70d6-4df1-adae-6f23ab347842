<?php

namespace App\Imports;

use App\Models\Material;
use App\Models\Course;
use App\Models\Lesson;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MaterialImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    private $logicErrors;
    private $count;
    private $totalCount;

    public function __construct()
    {
        $this->logicErrors = null;
        $this->count = 0;
        $this->totalCount = 0;
    }

    public function collection(Collection $collection)
    {
        DB::beginTransaction();

        $this->totalCount = $collection->count();

        foreach ($collection as $row) {
            if($row == null){
                continue;
            }

            // init value - matching the format from your screenshot
            $values = array_values($row->toArray());
            $materialTitle = $values[0] ?? null;  // Tiêu đề
            $contentUrl = $values[1] ?? null;     // URL
            $content = $values[2] ?? null;        // Nội dung
            $documentUrl = $values[3] ?? null;    // Giáo án
            $courseKey = $values[4] ?? null;      // Khóa học

            // Validate required fields
            if (empty($materialTitle)) {
                $this->count++;
                $this->logicErrors = "Tiêu đề is required at row " . $this->count;
                DB::rollBack();
                return;
            }

            if (empty($courseKey)) {
                $this->count++;
                $this->logicErrors = "Khóa học is required at row " . $this->count;
                DB::rollBack();
                return;
            }

            // Find course by key
            $course = Course::where('key', $courseKey)->first();
            if (!$course) {
                $this->count++;
                $this->logicErrors = "Course with key '{$courseKey}' not found at row " . $this->count;
                DB::rollBack();
                return;
            }

            // Find or create default lesson for the course
            $lesson = Lesson::where('course_id', $course->id)->first();

            if (!$lesson) {
                // Create default lesson for the course
                $lesson = new Lesson();
                $lesson->fill([
                    'title' => $course->title_en . ' - Materials',
                    'course_id' => $course->id,
                    'description' => 'Default lesson for course materials',
                ]);
                $lesson->save();
            }

            // Check if material already exists (including soft-deleted)
            $existingMaterial = Material::withTrashed()
                                      ->where('lesson_id', $lesson->id)
                                      ->where('title', $materialTitle)
                                      ->first();

            if ($existingMaterial) {
                if ($existingMaterial->trashed()) {
                    // Restore soft-deleted material and update its data
                    $existingMaterial->restore();
                    $existingMaterial->fill([
                        'content' => $content,
                        'content_url' => $contentUrl,
                        'document_url' => $documentUrl,
                    ]);
                    $existingMaterial->save();
                } else {
                    // Material exists and is not soft-deleted - this is a duplicate
                    $this->count++;
                    $this->logicErrors = "Material '{$materialTitle}' already exists in course '{$course->title_en}' at row " . $this->count;
                    DB::rollBack();
                    return;
                }
            } else {
                // Create new material
                $material = new Material();
                $material->fill([
                    'lesson_id' => $lesson->id,
                    'title' => $materialTitle,
                    'type' => 'document', // Default type since not specified in import format
                    'content' => $content,
                    'content_url' => $contentUrl,
                    'document_url' => $documentUrl,
                ]);
                $material->save();
            }
            $this->count++;
        }

        DB::commit();
    }

    public function getLogicErrors()
    {
        return $this->logicErrors;
    }

    public function importedCount()
    {
        return $this->count;
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
