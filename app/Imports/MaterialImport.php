<?php

namespace App\Imports;

use App\Models\Material;
use App\Models\Course;
use App\Models\Lesson;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MaterialImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    private $logicErrors;
    private $count;
    private $totalCount;

    public function __construct()
    {
        $this->logicErrors = null;
        $this->count = 0;
        $this->totalCount = 0;
    }

    public function collection(Collection $collection)
    {
        DB::beginTransaction();

        $this->totalCount = $collection->count();

        foreach ($collection as $row) {
            if($row == null){
                continue;
            }

            // init value - matching the format from your screenshot
            $values = array_values($row->toArray());
            $materialTitle = $values[0] ?? null;  // Tiêu đề
            $contentUrl = $values[1] ?? null;     // URL
            $content = $values[2] ?? null;        // Nội dung
            $documentUrl = $values[3] ?? null;    // Giáo án
            $courseKey = $values[4] ?? null;      // Khóa học

            // Validate required fields
            if (empty($courseKey)) {
                $this->count++;
                $this->logicErrors = "Course Key is required at row " . $this->count;
                DB::rollBack();
                return;
            }

            if (empty($lessonTitle)) {
                $this->count++;
                $this->logicErrors = "Lesson Title is required at row " . $this->count;
                DB::rollBack();
                return;
            }

            if (empty($materialTitle)) {
                $this->count++;
                $this->logicErrors = "Material Title is required at row " . $this->count;
                DB::rollBack();
                return;
            }

            if (empty($type) || !in_array($type, ['video', 'document', 'quiz'])) {
                $this->count++;
                $this->logicErrors = "Invalid material type at row " . $this->count . ". Must be: video, document, or quiz";
                DB::rollBack();
                return;
            }

            // Find course by key
            $course = Course::where('key', $courseKey)->first();
            if (!$course) {
                $this->count++;
                $this->logicErrors = "Course with key '{$courseKey}' not found at row " . $this->count;
                DB::rollBack();
                return;
            }

            // Find or create lesson
            $lesson = Lesson::where('course_id', $course->id)
                           ->where('title', $lessonTitle)
                           ->first();
            
            if (!$lesson) {
                // Create new lesson
                $lesson = new Lesson();
                $lesson->fill([
                    'title' => $lessonTitle,
                    'course_id' => $course->id,
                    'description' => 'Auto-created lesson from material import',
                ]);
                $lesson->save();
            }

            // Check if material already exists
            $existingMaterial = Material::where('lesson_id', $lesson->id)
                                      ->where('title', $materialTitle)
                                      ->first();
            
            if ($existingMaterial) {
                $this->count++;
                $this->logicErrors = "Material '{$materialTitle}' already exists in lesson '{$lessonTitle}' at row " . $this->count;
                DB::rollBack();
                return;
            }

            // Create material
            $material = new Material();
            $material->fill([
                'lesson_id' => $lesson->id,
                'title' => $materialTitle,
                'type' => $type,
                'content' => $content,
                'content_url' => $contentUrl,
                'document_url' => $documentUrl,
            ]);
            $material->save();
            $this->count++;
        }

        DB::commit();
    }

    public function getLogicErrors()
    {
        return $this->logicErrors;
    }

    public function importedCount()
    {
        return $this->count;
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
