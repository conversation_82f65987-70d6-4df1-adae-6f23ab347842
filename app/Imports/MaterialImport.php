<?php

namespace App\Imports;

use App\Models\Material;
use App\Models\Course;
use App\Models\Lesson;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MaterialImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    private $logicErrors;
    private $count;
    private $totalCount;

    public function __construct()
    {
        $this->logicErrors = null;
        $this->count = 0;
        $this->totalCount = 0;
    }

    public function collection(Collection $collection)
    {
        DB::beginTransaction();

        $this->totalCount = $collection->count();

        foreach ($collection as $rowIndex => $row) {
            if($row == null){
                continue;
            }

            // init value - matching the format from your screenshot
            $values = array_values($row->toArray());
            $materialTitle = $values[0] ?? null;  // Tiêu đề
            $contentUrl = $values[1] ?? null;     // URL
            $content = $values[2] ?? null;        // Nội dung
            $documentUrl = $values[3] ?? null;    // Giáo án
            $courseKey = $values[4] ?? null;      // Khóa học

            // Validate required fields
            if (empty($materialTitle)) {
                $this->logicErrors = "Tiêu đề is required at row " . ($rowIndex + 2); // +2 because of header row
                DB::rollBack();
                return;
            }

            if (empty($courseKey)) {
                $this->logicErrors = "Khóa học is required at row " . ($rowIndex + 2);
                DB::rollBack();
                return;
            }

            // Find course by key
            $course = Course::where('key', $courseKey)->first();
            if (!$course) {
                $this->logicErrors = "Course with key '{$courseKey}' not found at row " . ($rowIndex + 2);
                DB::rollBack();
                return;
            }

            // Find or create default lesson for the course
            $lesson = Lesson::where('course_id', $course->id)->first();

            if (!$lesson) {
                // Create default lesson for the course
                $lesson = new Lesson();
                $lesson->fill([
                    'title' => $course->title_en . ' - Materials',
                    'course_id' => $course->id,
                    'description' => 'Default lesson for course materials',
                ]);
                $lesson->save();
            }

            // No duplicate check - materials can have same names but different content
            // and materials are often reused across different contexts

            // Extract number from content's last 2 characters and add prefix to title
            $prefixedTitle = $materialTitle;
            if (!empty($content)) {
                // Get last 2 characters from content (e.g., "h1.19" -> "19")
                $lastTwoChars = substr($content, -2);
                if (is_numeric($lastTwoChars)) {
                    // Convert to integer to remove leading zeros (e.g., "01" -> 1)
                    $number = (int) $lastTwoChars;
                    $prefixedTitle = "Bài " . $number . " - " . $materialTitle;
                }
            }

            // Create material with precise timestamp to maintain order
            $material = new Material();
            $material->fill([
                'lesson_id' => $lesson->id,
                'title' => $prefixedTitle,
                'type' => 'document', // Default type since not specified in import format
                'content' => $content,
                'content_url' => $contentUrl,
                'document_url' => $documentUrl,
            ]);

            // Set precise timestamp to maintain CSV row order
            $baseTime = now();
            $material->created_at = $baseTime->copy()->addMicroseconds($rowIndex * 1000);
            $material->updated_at = $material->created_at;

            $material->save();
            $this->count++;
        }

        DB::commit();
    }

    public function getLogicErrors()
    {
        return $this->logicErrors;
    }

    public function importedCount()
    {
        return $this->count;
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
