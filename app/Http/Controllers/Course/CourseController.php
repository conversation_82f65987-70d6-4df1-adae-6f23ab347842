<?php

namespace App\Http\Controllers\Course;

use App\Http\Controllers\Controller;
use App\Http\Requests\Course\StoreRequest;
use App\Http\Requests\Course\UpdateRequest;
use App\Models\Account;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\Material;
use App\Exports\MaterialExport;
use App\Exports\MaterialTemplateExport;
use App\Imports\MaterialImport;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class CourseController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('show-course', Course::class);
        $keyword = $request->input('search');
        $viewMode = $request->input('view_mode', 'course'); // Default to course view
        $filterCourseId = $request->input('filter_course');
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');

        $user = Auth::user();
        $userClass = $user ? $user->class : null;

        // Get all courses for filter dropdown, filtered by user class
        $allCourses = $userClass ? Course::where('title_en', 'like', "%$userClass%") ->get() : Course::all();

        // Course view mode
        if ($viewMode === 'course') {
            $query = Course::query();

            if ($userClass) {
                $query = $query->where('title_en', 'like', "%$userClass%") ;
            }

            if($keyword != null){
                $query = $query->where(function($q) use ($keyword) {
                    $q->where('title_en', 'like', "%$keyword%")
                      ->orWhere('description_en', 'like', "%$keyword%");
                });
            }

            // Apply sorting
            if ($sortField && in_array($sortField, ['title_en', 'description_en', 'created_at'])) {
                $query->orderBy($sortField, $sortDirection);
            } else {
                $query->orderBy('created_at', 'desc');
            }

            $courses = $query->paginate(50);

            return view('course.index', compact('courses', 'keyword', 'viewMode', 'allCourses', 'filterCourseId', 'sortField', 'sortDirection'));
        }
        // Material view mode
        else {
            $query = Material::query()
                ->with('lesson')
                ->with('lesson.course');

            if ($userClass) {
                $query = $query->whereHas('lesson.course', function($q) use ($userClass) {
                    $q->where('title_en', 'like', "%$userClass%") ;
                });
            }

            if($keyword != null){
                $query = $query->where('title', 'like', "%$keyword%");
            }

            // Apply course filter
            if($filterCourseId != null){
                $query = $query->whereHas('lesson.course', function($q) use ($filterCourseId) {
                    $q->where('id', $filterCourseId);
                });
            }

            // Apply sorting
            if ($sortField && in_array($sortField, ['title', 'content', 'created_at'])) {
                $query->orderBy($sortField, $sortDirection);
            } else {
                $query->orderBy('created_at', 'desc');
            }

            $materials = $query->paginate(50);

            return view('course.index', compact('materials', 'keyword', 'viewMode', 'allCourses', 'filterCourseId', 'sortField', 'sortDirection'));
        }
    }

    public function create()
    {
        $this->authorize('create-course', Course::class);
        return view('course.create');
    }

    public function store(StoreRequest $request)
    {
        $this->authorize('create-course', Course::class);

        // set image
        $imageName = null;
        if($request->hasFile('image')){
            $imageName = rand(111, 999) . time() . '.' . $request->image->extension();
            $request->image->move(public_path('uploads/courses'), $imageName);
        }

        $imageThumbnail = null;
        if($request->hasFile('thumbnail_image')){
            $imageThumbnail = rand(111, 999) . time() . '.' . $request->thumbnail_image->extension();
            $request->thumbnail_image->move(public_path('uploads/courses/thumbnails'), $imageThumbnail);
        }

        Course::query()->create([
            'key' => $request->input('key'),
            'title_en' => $request->input('title_en'),
            'description_en' => $request->input('description_en'),
            'image' => $imageName,
            'thumbnail_image' => $imageThumbnail,
            'material_prefix' => $request->input('material_prefix'),
        ]);

        $this->flashMessage('check', 'Course successfully added!', 'success');
        return redirect()->route('course');
    }

    public function destroy($id): RedirectResponse
    {
        $this->authorize('destroy-course', Course::class);
        $course = Course::query()->find($id);

        if(!$course){
            $this->flashMessage('warning', 'Course not found!', 'danger');
            return redirect()->route('course');
        }

        $course->delete();
        $this->flashMessage('check', 'Course successfully deleted!', 'success');

        return redirect()->route('course');
    }

    /**
     * Bulk delete courses
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function bulkDestroy(Request $request): RedirectResponse
    {
        $this->authorize('destroy-course', Course::class);
        $ids = explode(',', $request->input('ids'));

        if (empty($ids)) {
            $this->flashMessage('warning', 'No courses selected!', 'danger');
            return redirect()->route('course');
        }

        $deletedCount = 0;
        foreach ($ids as $id) {
            $course = Course::query()->find($id);
            if ($course) {
                $course->delete();
                $deletedCount++;
            }
        }

        if ($deletedCount > 0) {
            $this->flashMessage('check', $deletedCount . ' courses successfully deleted!', 'success');
        } else {
            $this->flashMessage('warning', 'No courses were deleted!', 'danger');
        }

        return redirect()->route('course');
    }

    public function edit($id)
    {
        $this->authorize('edit-course', Account::class);
        $course = Course::query()
            ->find($id);

        return view('course.edit',compact('course'));
    }

    public function update(UpdateRequest $request, $id): RedirectResponse
    {
        $this->authorize('edit-course', Course::class);
        $course = Course::query()->where('id', $id)->first();

        if(!$course){
            $this->flashMessage('warning', 'Course not found!', 'danger');
            return redirect()->route('account.edit', $course->id);
        }

        $oldImage = $course->image;
        $oldThumbnailImage = $course->thumbnail_image;

        // set image
        $imageName = null;
        if($request->hasFile('image')){
            $imageName = rand(111, 999) . time() . '.' . $request->image->extension();
            try {
                unlink(public_path('uploads/courses/' . $oldImage));
            } catch (\Exception $e){

            }
            $request->image->move(public_path('uploads/courses'), $imageName);
        } else {
            $imageName = $oldImage;
        }

        $imageThumbnail = null;
        if($request->hasFile('thumbnail_image')){
            $imageThumbnail = rand(111, 999) . time() . '.' . $request->thumbnail_image->extension();
            try {
                unlink(public_path('uploads/courses/thumbnails/' . $oldThumbnailImage));
            } catch (\Exception $e){

            }
            $request->thumbnail_image->move(public_path('uploads/courses/thumbnails'), $imageThumbnail);
        } else {
            $imageThumbnail = $oldThumbnailImage;
        }

        $course->fill([
            'key' => $request->input('key'),
            'title_en' => $request->input('title_en'),
            'description_en' => $request->input('description_en'),
            'image' => $imageName,
            'thumbnail_image' => $imageThumbnail,
            'material_prefix' => $request->input('material_prefix'),
        ]);
        $course->save();

        $this->flashMessage('check', 'Course successfully edited!', 'success');
        return redirect()->route('course');
    }

    public function exportMaterials()
    {
        // Simple test to see if route is working
        return response('Export route is working!', 200)
            ->header('Content-Type', 'text/plain');
    }

    public function exportMaterialsTemplate()
    {
        return Excel::download(new MaterialTemplateExport(), 'materials_template.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function importMaterials(Request $request)
    {
        $this->authorize('edit-course', Course::class);
        ini_set('max_execution_time', 0);
        $path = $request->file('file');
        $import = new MaterialImport();
        Excel::import($import, $path);
        $failures = $import->getLogicErrors();

        if ($failures != null) {
            $this->flashMessage('warning', $failures, 'danger');
            return redirect()->route('course', ['view_mode' => 'material']);
        }

        if ($import->importedCount() === 0) {
            $this->flashMessage('warning', __('messages.alert.add.empty_records'), 'danger');
            return redirect()->route('course', ['view_mode' => 'material']);
        }

        $this->flashMessage('check', __('messages.alert.add.import_done', ['count' => $import->importedCount()]), 'success');
        return redirect()->route('course', ['view_mode' => 'material']);
    }
}
