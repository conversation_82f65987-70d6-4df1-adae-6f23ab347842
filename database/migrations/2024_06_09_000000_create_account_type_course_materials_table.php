<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_type_course_materials', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('account_type_id');
            $table->unsignedBigInteger('course_id');
            $table->text('lesson_list')->nullable(); // Comma-separated list of materials for this course
            $table->boolean('auto_manage_materials')->default(false);
            $table->integer('max_materials')->default(50);
            $table->integer('materials_add_count')->default(5);
            $table->string('materials_update_frequency')->default('weekly'); // e.g. 'daily', 'weekly', 'monthly', '2 weeks', '2 months', 'yearly', etc.
            $table->time('materials_update_time')->default('02:00:00'); // Time of day for update (e.g., 02:00:00 for 2AM)
            $table->timestamp('last_materials_update')->nullable();
            $table->timestamps();

            $table->unique(['account_type_id', 'course_id']);
            $table->foreign('account_type_id')->references('id')->on('account_types')->onDelete('cascade');
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('account_type_course_materials');
    }
}; 