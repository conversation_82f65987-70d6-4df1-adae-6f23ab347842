<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->boolean('phone_number_as_password')->default(true)->after('password');
            $table->string('password_manual')->nullable()->after('phone_number_as_password');
        });
    }

    public function down()
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropColumn('phone_number_as_password');
            $table->dropColumn('password_manual');
        });
    }
}; 