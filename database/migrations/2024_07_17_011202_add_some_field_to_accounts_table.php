<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSomeFieldToAccountsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->integer('is_mamnon')->nullable();
            $table->integer('is_tieuhoc')->nullable();
            $table->integer('mamnon_account_type_id')->nullable();
            $table->integer('tieuhoc_account_type_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropColumn('is_mamnon');
            $table->dropColumn('is_tieuhoc');
            $table->dropColumn('mamnon_account_type_id');
            $table->dropColumn('tieuhoc_account_type_id');
        });
    }
}
