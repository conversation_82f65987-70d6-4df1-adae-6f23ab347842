<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMaterialManagementSettingsToAccountTypes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('account_types', function (Blueprint $table) {
            $table->boolean('auto_manage_materials')->default(false);
            $table->integer('max_materials')->default(50);
            $table->integer('materials_add_count')->default(5);
            $table->enum('materials_update_frequency', ['daily', 'weekly', 'monthly'])->default('weekly');
            $table->timestamp('last_materials_update')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('account_types', function (Blueprint $table) {
            $table->dropColumn('auto_manage_materials');
            $table->dropColumn('max_materials');
            $table->dropColumn('materials_add_count');
            $table->dropColumn('materials_update_frequency');
            $table->dropColumn('last_materials_update');
        });
    }
}
