@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.add.course'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('course') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.course') }}
        </a>
    </li>

@endsection

@section('content')

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('course.store') }}" method="post" enctype="multipart/form-data">
                        {{ csrf_field() }}
                        <input type="hidden" name="active" value="1">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('key') ? 'has-error' : '' }}">
                                    <label for="key">{{ __('messages.key') }}</label>
                                    <input type="text" name="key" class="form-control" maxlength="50" placeholder="{{ __('messages.key') }}" value="{{ old('key') }}">
                                    @if($errors->has('key'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('key') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('title_en') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.title') }}</label>
                                    <input type="text" name="title_en" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.title') }}" required="" value="{{ old('title_en') }}" autofocus>
                                    @if($errors->has('title_en'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('title_en') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('description_en') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.description') }}</label>
                                    <input type="text" name="description_en" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.description') }}" required="" value="{{ old('description_en') }}" autofocus>
                                    @if($errors->has('description_en'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('description_en') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('image') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.image') }}</label>
                                    <input type="file" name="image" class="form-control" placeholder="{{ __('messages.image') }}" required="" value="{{ old('image') }}">
                                    @if($errors->has('image'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('image') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('thumbnail_image') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.thumbnail_image') }}</label>
                                    <input type="file" name="thumbnail_image" class="form-control" placeholder="{{ __('messages.thumbnail_image') }}" required="" value="{{ old('thumbnail_image') }}">
                                    @if($errors->has('thumbnail_image'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('thumbnail_image') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <div class="col-lg-6"></div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.add') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

<script>
    $(function(){
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return "Nenhum registro encontrado.";
                }
            }
        });
    });

</script>

@endsection
